<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <CheckBoxPreference
        android:defaultValue="false"
        android:key="@string/ECR_ENABLE"
        android:summaryOff="@string/no"
        android:summaryOn="@string/yes"
        android:title="@string/ecr_enable" />

    <ListPreference
        android:defaultValue="@string/base_rs232"
        android:dialogTitle="@string/ecr_menu_mode_choose"
        android:dependency="@string/ECR_ENABLE"
        android:entries="@array/ecr_menu_comm_mode_list_entries"
        android:entryValues="@array/ecr_menu_comm_mode_values_list_entries"
        android:key="@string/ECR_COMM"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/ecr_type" />

    <Preference
        android:title="@string/ecr_wifi_ip"
        android:summary="@string/ecr_wifi_ip_refresh"
        android:dependency="@string/ECR_ENABLE" />

    <com.pax.view.EditTextPreferenceFix
        android:defaultValue="5000"
        android:capitalize="words"
        android:dependency="@string/ECR_ENABLE"
        android:ems="10"
        android:key="@string/ECR_WIFI_PORT"
        android:maxLength="5"
        android:maxLines="1"
        android:inputType="number"
        android:selectAllOnFocus="true"
        android:singleLine="true"
        android:title="@string/ecr_wifi_port" />

</PreferenceScreen>