/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-12-27
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.Window;

import com.pax.edc.R;
import com.pax.edc.opensdk.TransResult;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.base.Acquirer;
import com.pax.pay.base.CardRange;
import com.pax.pay.base.Issuer;
import com.pax.pay.base.YUUCardRange;
import com.pax.pay.emv.EmvAid;
import com.pax.pay.emv.EmvCapk;
import com.pax.pay.trans.model.AcqManager;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.view.dialog.DialogUtils;
import java.util.List;

public class InitializeInputPwdActivity extends BaseActivity {

    public static final int REQ_WIZARD = 1;

    public static boolean onCheckInit(Activity activity, int requestCode) {
        if (FinancialApplication.getController().isFirstRun()) {
            Intent intent = new Intent(activity, InitializeInputPwdActivity.class);
            activity.startActivityForResult(intent, requestCode);
            return true;
        }
        return false;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);

        //start wizard activity with delay to avoid memory leak
        new Handler().postDelayed(() -> {
            if (!isFinishing()) {
                Intent intent = new Intent(InitializeInputPwdActivity.this, WizardActivity.class);
                startActivityForResult(intent, REQ_WIZARD);
            }
        }, 1000);
    }

    @Override
    protected void loadParam() {
        // do nothing
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_initialize_pwd_layout;
    }

    @Override
    protected void initViews() {
    }

    @Override
    protected void setListeners() {
    }

    @Override
    protected boolean onKeyBackDown() {
        // exit app
        exit();
        return true;
    }




    protected void onReqWizard() {
        //remain for process result
        Intent intent = getIntent();
        setResult(RESULT_OK, intent);
        insertAcquirer();
        initEMVParam();
        FinancialApplication.getController().set(Controller.IS_FIRST_RUN, false);
        finish();
    }

    private void exit() {
        DialogUtils.showExitAppDialog(InitializeInputPwdActivity.this);
        setResult(RESULT_CANCELED);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQ_WIZARD) {
            onReqWizard();
        }
    }

    /**
     *
     */
    private int initEMVParam() {
        ToastUtils.showMessage(R.string.emv_param_load);
        FinancialApplication.getApp().runInBackground(() -> {
            // emv公钥下载
            Controller controller = FinancialApplication.getController();
            if (controller.get(Controller.NEED_DOWN_CAPK) == Controller.Constant.YES) {
                EmvCapk.load(Utils.readObjFromJSON("capk.json", EmvCapk.class));
                controller.set(Controller.NEED_DOWN_CAPK, Controller.Constant.NO);
            }
            // emv 参数下载
            if (controller.get(Controller.NEED_DOWN_AID) == Controller.Constant.YES) {
                EmvAid.load(Utils.readObjFromJSON("aid.json", EmvAid.class));
                controller.set(Controller.NEED_DOWN_AID, Controller.Constant.NO);
            }
            // Use safe UI thread execution to prevent memory leaks
            safeRunOnUiThread(() ->
                ToastUtils.showMessage(R.string.emv_init_succ)
            );
        });
        return TransResult.SUCC;
    }

    private void insertAcquirer() {
        AcqManager acqManager = FinancialApplication.getAcqManager();
        List<Acquirer> acquirers = Utils.readObjFromJSON("acquirer.json", Acquirer.class);
        List<Issuer> issuers = Utils.readObjFromJSON("issuer.json", Issuer.class);
        List<CardRange> cardRanges = Utils.readObjFromJSON("card_range.json", CardRange.class);
        List<YUUCardRange> yuuCardRanges =
                Utils.readObjFromJSON("card_range_yuu.json", YUUCardRange.class);

        for (Issuer issuer : issuers) {
            for (CardRange cardRange : cardRanges) {
                if (cardRange.getName().equals(issuer.getName())) {
                    cardRange.setIssuer(issuer);
                }
            }
        }

        acqManager.insertAllAcquirer(acquirers);

        acqManager.insertAllIssuer(issuers);

        acqManager.insertAllCardRange(cardRanges);

        acqManager.insertAllYUUCardRange(yuuCardRanges);
    }

}
