/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-12-30
 * Module Author: lixc
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.widget.TextView;
import com.pax.commonlib.RxUtils;
import com.pax.edc.R;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.menu.TransMenuActivity;
import com.pax.pay.trans.component.Component;
import com.pax.pay.trans.model.Controller;
import com.pax.pay.utils.Utils;
import io.reactivex.functions.Action;

import java.lang.ref.WeakReference;

/**
 * The type Self test activity.
 */
public class SelfTestActivity extends AppCompatActivity {

    private static final String TAG = "SelfTest";

    /**
     * The constant REQ_INITIALIZE.
     */
    public static final int REQ_INITIALIZE = 1;

    private boolean isFirstRun = true;

    /**
     * On self test.
     *
     * @param activity the activity
     * @param requestCode the request code
     */
    public static void onSelfTest(Activity activity, int requestCode) {
        Intent intent = new Intent(activity, SelfTestActivity.class);
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_selftest_layout);

        boolean isInstalledNeptune = Component.neptuneInstalled(this, new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface arg0) {
                finishFailed();
            }
        });

        if (!isInstalledNeptune) {
            return;
        }

        FinancialApplication.getController().set(Controller.NEED_SET_WIZARD, Controller.Constant.YES);
        onCheckLog();

        isFirstRun = InitializeInputPwdActivity.onCheckInit(SelfTestActivity.this, REQ_INITIALIZE);

        //FinancialApplication.getSysParam().init();

        if (!isFirstRun)
            onActivityResult(REQ_INITIALIZE, 0, null);
    }

    private void onCheckLog() {
        if (FinancialApplication.getController().get(Controller.CLEAR_LOG) == Controller.Constant.YES
                && FinancialApplication.getTransDataDbHelper().deleteAllTransData()
                && FinancialApplication.getTransTotalDbHelper().deleteAllTransTotal()) {
            FinancialApplication.getController().set(Controller.CLEAR_LOG, Controller.Constant.NO);
            FinancialApplication.getController().set(Controller.BATCH_UP_STATUS, Controller.Constant.WORKED);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, final int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (REQ_INITIALIZE == requestCode) {
            if (Utils.isA920Pro()) {
                RxUtils.addDisposable(Utils.callPermission(SelfTestActivity.this, new Action() {
                            @Override
                            public void run() throws Exception {
                                Log.e(TAG, "{run}");// 执行顺序——2
                                onHandleResult(resultCode);
                            }
                        }, getString(R.string.permission_rationale), Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.SEND_SMS));
            } else {
                RxUtils.addDisposable(Utils.callPermission(SelfTestActivity.this, new Action() {
                            @Override
                            public void run() throws Exception {
                                Log.e(TAG, "{run}");// 执行顺序——2
                                onHandleResult(resultCode);
                            }
                        }, getString(R.string.permission_rationale), Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.SEND_SMS));
            }
        }
    }

    private void onHandleResult(final int resultCode) {
        if (resultCode == RESULT_OK) {
            FinancialApplication.getApp().runInBackground(new CounterThread());
        } else {
            finishOk();
        }
    }

    private class CounterThread implements Runnable {
        @Override
        public void run() {
            if (isFirstRun) {
                SystemClock.sleep(3000);
            }
            // Use safe UI thread execution to prevent memory leaks
            safeRunOnUiThread(() -> {
                TextView textView = (TextView) findViewById(R.id.selfTest);
                textView.setText(getString(R.string.selfTest_succ));
                finishOk();
            });
        }
    }

    private void finishOk() {
        Intent intent = getIntent();
        setResult(RESULT_OK, intent);
        finish();
    }

    private void finishFailed() {
        Intent intent = getIntent();
        setResult(RESULT_CANCELED, intent);
        finish();
    }

    /**
     * Safely execute a Runnable on the UI thread using WeakReference to prevent memory leaks.
     * This method ensures that the Activity is still valid and not destroyed before executing
     * the UI update, breaking the strong reference chain that could cause memory leaks.
     *
     * @param runnable The Runnable to execute on the UI thread
     */
    private void safeRunOnUiThread(Runnable runnable) {
        WeakReference<SelfTestActivity> weakActivity = new WeakReference<>(this);

        FinancialApplication.getApp().runOnUiThread(() -> {
            SelfTestActivity activity = weakActivity.get();
            if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
                try {
                    runnable.run();
                } catch (Exception e) {
                    Log.w(TAG, "Error executing UI thread operation: " + e.getMessage(), e);
                }
            } else {
                Log.d(TAG, "Activity is null, finishing, or destroyed. Skipping UI thread operation to prevent memory leak.");
            }
        });
    }

    @Override
    protected void onDestroy() {
        RxUtils.release();
        super.onDestroy();
    }
}
