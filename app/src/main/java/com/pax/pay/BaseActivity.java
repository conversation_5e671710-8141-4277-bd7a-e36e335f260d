/*
 * ============================================================================
 * COPYRIGHT
 *              Pax CORPORATION PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with Pax Corporation and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *      Copyright (C) 2016 - ? Pax Corporation. All rights reserved.
 * Module Date: 2016-11-25
 * Module Author: Steven.W
 * Description:
 *
 * ============================================================================
 */
package com.pax.pay;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Bundle;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;

import com.pax.ecrsdk.server.param.Param;
import com.pax.edc.BuildConfig;
import com.pax.edc.R;
import com.pax.pay.app.ActivityStack;
import com.pax.pay.app.FinancialApplication;
import com.pax.pay.app.quickclick.QuickClickProtection;
import com.pax.pay.utils.ToastUtils;
import com.pax.pay.utils.Utils;
import com.pax.settings.SysParam;
import com.pax.settings.base.MicroPayReceiver;
import com.pax.settings.base.ReceiverListener;
import com.pax.settings.base.WifiReceiver;
import com.pax.settings.host.EcrFragment;

import java.lang.ref.WeakReference;
import java.lang.reflect.Method;

import static com.pax.pay.app.FinancialApplication.getSysParam;
import static com.pax.pay.utils.Utils.changeAppLanguageInApp;

public abstract class BaseActivity extends AppCompatActivity implements View.OnClickListener {

    protected static final String TAG = "BaseActivity";
    protected QuickClickProtection quickClickProtection = QuickClickProtection.getInstance();
    private ActionBar mActionBar;

    private WifiReceiver mWifiReceiver;
    private MicroPayReceiver microPayReceiver;

    private ReceiverListener mReceiverListener = connectedStatus -> {
        if (!connectedStatus) {
            //wifi disconnected
            FinancialApplication.getBaseManager().disconnectBNBase();
            //关掉底座后关闭相关ecr信息
            String ecrType = FinancialApplication.getServerManager().getECRType();
            if (ecrType != null && (ecrType.equals(Param.BASE_RS232))) {
                FinancialApplication.getServerManager().closeServer();
            }
            updateEcrStatus();
        } else {
            if (FinancialApplication.getSysParam().get(SysParam.BooleanParam.ECR_ENABLE)) {
                FinancialApplication.getApp().runInBackground(() -> {
                    try {
                        String currentSsid = Utils.getWifiSSID(FinancialApplication.getApp());
                        Log.i(TAG, "wifi status changed currentSsid is " + currentSsid);
                        if (currentSsid != null) {
                            Log.i(TAG, "ECR enabled. Attempting silent base reconnection from BaseActivity.");
                            // connectBNBase 会更新 SysParam.BooleanParam.BASE_ENABLE
                            Log.d(TAG, "About to call connectBNBase()...");
                            final int connectResult = FinancialApplication.getBaseManager().connectBNBase();
                            Log.i(TAG, "connectBNBase() returned: " + connectResult);

                            if (connectResult == 1) {
                                Log.i(TAG, "Base connection successful, starting ECR server...");
                                String type = getSysParam().get(SysParam.StringParam.ECR_COMM);
                                Log.d(TAG, "ECR communication type: " + type);
                                FinancialApplication.getServerManager().setParam(EcrFragment.getEcrParam(type));
                                FinancialApplication.getServerManager().startServer();
                                Log.i(TAG, "Silent base reconnection successful from BaseActivity.");
                            } else {
                                Log.w(TAG, "Silent base reconnection failed from BaseActivity. Result: " + connectResult);
                            }

                            // Use safe UI thread execution to prevent memory leaks
                            Log.d(TAG, "About to call safeRunOnUiThread for ECR status refresh...");
                            safeRunOnUiThread(this::refreshEcrStatus);
                        } else {
                            Log.w(TAG, "currentSsid is null, skipping ECR reconnection");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Exception during ECR reconnection: " + e.getMessage(), e);
                    }
                });
            } else {
                Log.i(TAG, "Wi-Fi connected, but ECR is not enabled. No base reconnection attempt from BaseActivity.");
            }
        }
    };

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(changeAppLanguageInApp(newBase));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!BuildConfig.DEBUG) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);//禁用截屏
        }
        setContentView(getLayoutId());
        WeakReference<BaseActivity> weakReference = new WeakReference<>(this);
        ActivityStack.getInstance().push(weakReference.get());

        loadParam(); //AET-274

        mActionBar = getSupportActionBar();
        if (mActionBar != null) {
            mActionBar.setTitle(getTitleString());
            mActionBar.setDisplayHomeAsUpEnabled(true);
            mActionBar.setDisplayShowTitleEnabled(true);
        }

        mWifiReceiver = new WifiReceiver(mReceiverListener);
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");//监视网络状态连接
        registerReceiver(mWifiReceiver, filter);

        microPayReceiver = new MicroPayReceiver();
        IntentFilter filter1 = new IntentFilter();
        filter1.addAction("com.micropay.broadcast.MICRO_PAY_RUNNING");//监听micro pay是否是单机运行
        registerReceiver(microPayReceiver, filter1);

        initViews();

        setListeners();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        if (this.equals(ActivityStack.getInstance().top())) {
            ActivityStack.getInstance().pop();
        }
        super.onDestroy();
        if (mWifiReceiver != null) {
            unregisterReceiver(mWifiReceiver);
            mWifiReceiver = null;
        }
        if (microPayReceiver != null) {
            unregisterReceiver(microPayReceiver);
            microPayReceiver = null;
        }
        quickClickProtection.stop();
        Log.i("onDestroy", "");
    }

    /**
     * get layout ID
     *
     * @return
     */
    protected abstract int getLayoutId();

    /**
     * views initial
     */
    protected abstract void initViews();

    /**
     * set listeners
     */
    protected abstract void setListeners();

    /**
     * load parameter
     */
    protected abstract void loadParam();

    protected void updateEcrStatus() {
    }

    protected void refreshEcrStatus() {
    }

    /**
     * Safely execute a Runnable on the UI thread using WeakReference to prevent memory leaks.
     * This method ensures that the Activity is still valid and not destroyed before executing
     * the UI update, breaking the strong reference chain that could cause memory leaks.
     *
     * @param runnable The Runnable to execute on the UI thread
     */
    protected void safeRunOnUiThread(Runnable runnable) {
        WeakReference<BaseActivity> weakActivity = new WeakReference<>(this);
        FinancialApplication.getApp().runOnUiThread(() -> {
            BaseActivity activity = weakActivity.get();
            // Only check if Activity is still alive - allow paused/resumed activities to proceed
            // This ensures ECR status refresh works correctly while still preventing memory leaks
            if (activity != null && !activity.isDestroyed()) {
                try {
                    runnable.run();
                    Log.d(TAG, "Successfully executed UI thread operation for ECR status refresh");
                } catch (Exception e) {
                    Log.w(TAG, "Error executing UI thread operation: " + e.getMessage(), e);
                }
            } else {
                Log.d(TAG, "Activity is null or destroyed. Skipping UI thread operation to prevent memory leak.");
            }
        });
    }

    /**
     * 创建菜单选项视图
     *
     * @param menu menu
     * @return true/false
     */
    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        if (menu != null && menu.getClass().getSimpleName().equals("MenuBuilder")) {
            try {
                Method m = menu.getClass().getDeclaredMethod("setOptionalIconsVisible", Boolean.TYPE);
                m.setAccessible(true);
                m.invoke(menu, true);
            } catch (Exception e) {
                Log.e(getClass().getSimpleName(), "onMenuOpened...unable to set icons for overflow menu", e);
            }
        }
        return super.onPrepareOptionsMenu(menu);
    }

    // AET-93
    @Override
    public final void onClick(View v) {
        if (quickClickProtection.isStarted()) {
            return;
        }
        quickClickProtection.start();

        onClickProtected(v);
    }

    @Override
    protected void onStop() {
        super.onStop();
        ToastUtils.hideToast();
    }

    protected void onClickProtected(View v) {
        //do nothing
    }

    /**
     * 按下按钮, 触发事件
     *
     * @param keyCode 按键id
     * @param event   事件
     * @return true/false
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (quickClickProtection.isStarted()) { //AET-123
            return true;
        }
        quickClickProtection.start();
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return onKeyBackDown();
        }
        return super.onKeyDown(keyCode, event);
    }

    protected boolean onKeyBackDown() {
        finish();
        return true;
    }

    /**
     * 选择按钮的回调
     *
     * @param item 按钮选项
     * @return true/false
     */
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (quickClickProtection.isStarted()) { //AET-123
            return true;
        }
        quickClickProtection.start();
        return onOptionsItemSelectedSub(item);
    }

    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    protected String getTitleString() {
        return getString(R.string.app_name);
    }

    protected void enableBackAction(boolean enableBack) {
        if (mActionBar != null)
            mActionBar.setDisplayHomeAsUpEnabled(enableBack);
    }

    /**
     * 设置是否显示ActionBar
     *
     * @param showActionBar true 显示 false 隐藏
     */
    protected void enableActionBar(boolean showActionBar) {
        if (mActionBar != null) {
            if (showActionBar) {
                mActionBar.show();
            } else {
                mActionBar.hide();
            }
        }
    }
}
